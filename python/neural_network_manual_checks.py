"""
Neural Network implementation with MANUAL DIMENSION CHECKING
MNIST Architecture: 784 -> 256 -> 128 -> 10

This variant implements custom runtime dimension validation throughout
the forward and backward pass. Demonstrates explicit shape checking
as an alternative to compile-time type safety.

Features:
- Manual tensor shape assertions throughout forward/backward pass
- Runtime error detection for dimension mismatches
- Custom dimension validation hooks
- Comprehensive shape logging for debugging

Part of comparison: Idris dependent types vs Python runtime checking.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, List, Optional, Dict, Any, Union
import numpy as np
import time
import logging
from pathlib import Path

# Set up logging for dimension checks
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DimensionError(Exception):
    """Custom exception for dimension mismatches"""
    pass


class ManualDimensionChecker:
    """Utility class for manual dimension checking"""
    
    @staticmethod
    def check_tensor_shape(tensor: torch.Tensor, 
                          expected_shape: Tuple[int, ...], 
                          tensor_name: str = "tensor") -> None:
        """Check if tensor has expected shape, raise error if not"""
        actual_shape = tuple(tensor.shape)
        if actual_shape != expected_shape:
            raise DimensionError(
                f"{tensor_name} shape mismatch: expected {expected_shape}, "
                f"got {actual_shape}"
            )
    
    @staticmethod
    def check_batch_dimension(tensor: torch.Tensor, 
                            expected_batch_size: int,
                            tensor_name: str = "tensor") -> None:
        """Check if tensor has expected batch dimension"""
        actual_batch_size = tensor.shape[0]
        if actual_batch_size != expected_batch_size:
            raise DimensionError(
                f"{tensor_name} batch size mismatch: expected {expected_batch_size}, "
                f"got {actual_batch_size}"
            )
    
    @staticmethod
    def check_feature_dimension(tensor: torch.Tensor,
                              expected_features: int,
                              tensor_name: str = "tensor") -> None:
        """Check if tensor has expected feature dimension"""
        actual_features = tensor.shape[-1]
        if actual_features != expected_features:
            raise DimensionError(
                f"{tensor_name} feature dimension mismatch: expected {expected_features}, "
                f"got {actual_features}"
            )
    
    @staticmethod
    def log_tensor_info(tensor: torch.Tensor, name: str) -> None:
        """Log tensor shape and statistics for debugging"""
        logger.info(f"{name}: shape={tensor.shape}, dtype={tensor.dtype}, "
                   f"min={tensor.min().item():.4f}, max={tensor.max().item():.4f}")


class MNISTNetworkManualChecks(nn.Module):
    """Neural Network with comprehensive manual dimension checking
    
    This implementation adds explicit runtime checks at every step
    to catch dimension errors that would be prevented at compile-time
    in a dependently-typed language like Idris.
    """

    def __init__(self,
                 input_dim: int = 784,    # 28x28 pixels
                 hidden1_dim: int = 256,  # first hidden layer
                 hidden2_dim: int = 128,  # second hidden layer
                 output_dim: int = 10,    # 10 digit classes
                 enable_logging: bool = False):
        super(MNISTNetworkManualChecks, self).__init__()
        
        # Store dimensions for validation
        self.input_dim = input_dim
        self.hidden1_dim = hidden1_dim
        self.hidden2_dim = hidden2_dim
        self.output_dim = output_dim
        self.enable_logging = enable_logging
        
        # Layer definitions with explicit dimension documentation
        self.layer1 = nn.Linear(input_dim, hidden1_dim)   # 784 -> 256
        self.layer2 = nn.Linear(hidden1_dim, hidden2_dim) # 256 -> 128  
        self.layer3 = nn.Linear(hidden2_dim, output_dim)  # 128 -> 10
        
        # Initialize dimension checker
        self.dim_checker = ManualDimensionChecker()
        
        # Initialize weights
        self._initialize_weights()
        
        # Track dimension check statistics
        self.dimension_checks_performed = 0
        self.dimension_errors_caught = 0
    
    def _initialize_weights(self) -> None:
        """Initialize weights using Xavier initialization"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.zeros_(module.bias)
    
    def _validate_input(self, x: torch.Tensor) -> None:
        """Comprehensive input validation with manual checks"""
        self.dimension_checks_performed += 1
        
        # Check tensor dimensionality
        if x.dim() != 2:
            self.dimension_errors_caught += 1
            raise DimensionError(f"Input must be 2D tensor, got {x.dim()}D")
        
        # Check feature dimension
        self.dim_checker.check_feature_dimension(
            x, self.input_dim, "input tensor"
        )
        
        # Log input info if enabled
        if self.enable_logging:
            self.dim_checker.log_tensor_info(x, "input")
    
    def _validate_layer_output(self, tensor: torch.Tensor, 
                              expected_features: int,
                              layer_name: str) -> None:
        """Validate layer output dimensions"""
        self.dimension_checks_performed += 1
        
        # Check output feature dimension
        self.dim_checker.check_feature_dimension(
            tensor, expected_features, f"{layer_name} output"
        )
        
        # Log layer output if enabled
        if self.enable_logging:
            self.dim_checker.log_tensor_info(tensor, f"{layer_name} output")

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass with comprehensive dimension checking
        
        Args:
            x: Input tensor of shape [batch_size, 784]
            
        Returns:
            Output tensor of shape [batch_size, 10]
            
        Raises:
            DimensionError: If any dimension mismatch is detected
        """
        batch_size = x.shape[0]
        
        # Validate input dimensions
        self._validate_input(x)
        
        # Layer 1: 784 -> 256 with validation
        h1 = self.layer1(x)
        self._validate_layer_output(h1, self.hidden1_dim, "layer1")
        h1 = F.relu(h1)
        
        # Layer 2: 256 -> 128 with validation
        h2 = self.layer2(h1)
        self._validate_layer_output(h2, self.hidden2_dim, "layer2")
        h2 = F.relu(h2)
        
        # Layer 3: 128 -> 10 with validation
        output = self.layer3(h2)
        self._validate_layer_output(output, self.output_dim, "layer3")
        
        # Final output validation
        expected_output_shape = (batch_size, self.output_dim)
        self.dim_checker.check_tensor_shape(
            output, expected_output_shape, "final output"
        )
        
        return output
    
    def predict(self, x: torch.Tensor) -> torch.Tensor:
        """Make prediction with dimension validation"""
        if x.dim() == 1:
            x = x.unsqueeze(0)  # Add batch dimension
        
        with torch.no_grad():
            output = self.forward(x)
            return torch.argmax(output, dim=1)
    
    def get_dimension_stats(self) -> Dict[str, int]:
        """Get statistics about dimension checking"""
        return {
            'checks_performed': self.dimension_checks_performed,
            'errors_caught': self.dimension_errors_caught,
            'success_rate': (self.dimension_checks_performed - self.dimension_errors_caught) / 
                           max(1, self.dimension_checks_performed)
        }
    
    def reset_dimension_stats(self) -> None:
        """Reset dimension checking statistics"""
        self.dimension_checks_performed = 0
        self.dimension_errors_caught = 0


def demonstrate_manual_checking():
    """Demonstrate manual dimension checking capabilities"""
    print("=== Manual Dimension Checking Demonstration ===")
    
    # Create model with logging enabled
    model = MNISTNetworkManualChecks(enable_logging=True)
    
    print("\\n1. Valid input test:")
    try:
        valid_input = torch.randn(32, 784)  # Correct shape
        output = model(valid_input)
        print(f"✓ Success: {valid_input.shape} -> {output.shape}")
    except DimensionError as e:
        print(f"✗ Unexpected error: {e}")
    
    print("\\n2. Invalid input dimension test:")
    try:
        invalid_input = torch.randn(32, 800)  # Wrong feature dimension
        output = model(invalid_input)
        print("✗ Should have failed!")
    except DimensionError as e:
        print(f"✓ Correctly caught error: {e}")
    
    print("\\n3. Wrong tensor dimensionality test:")
    try:
        wrong_dim_input = torch.randn(784)  # 1D instead of 2D
        output = model(wrong_dim_input)
        print("✗ Should have failed!")
    except DimensionError as e:
        print(f"✓ Correctly caught error: {e}")
    
    # Show dimension checking statistics
    stats = model.get_dimension_stats()
    print(f"\\nDimension checking statistics:")
    print(f"  Checks performed: {stats['checks_performed']}")
    print(f"  Errors caught: {stats['errors_caught']}")
    print(f"  Success rate: {stats['success_rate']:.2%}")


if __name__ == "__main__":
    demonstrate_manual_checking()
