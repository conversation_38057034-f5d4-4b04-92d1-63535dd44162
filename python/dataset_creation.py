"""
Dataset Creation and Verification System
For Neural Network Layer Compatibility Verification Experiments

Creates datasets with increasing architectural challenges to test
type safety and dimension tracking across different implementations:

1. Simple Dataset: Iris (150 samples, 4 features, 3 classes)
   - Baseline for type safety verification
   - Small enough for quick testing
   - Well-known dataset for validation

2. Medium Dataset: Synthetic (1000 samples, 8 features, 5 classes)
   - Moderate complexity for intermediate testing
   - Custom synthetic data with controlled properties

3. Complex Dataset: High-dimensional (5000 samples, 12 features, 7 classes)
   - Stress-test dimension tracking
   - Large enough to reveal performance differences
   - Complex enough to test robustness

All datasets are designed to test layer compatibility verification.
"""

import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader, TensorDataset
from sklearn.datasets import load_iris, make_classification
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from typing import Tuple, Dict, Any, List
import json
from pathlib import Path
from dataclasses import dataclass
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class DatasetInfo:
    """Information about a dataset"""
    name: str
    num_samples: int
    num_features: int
    num_classes: int
    train_samples: int
    test_samples: int
    feature_range: Tuple[float, float]
    class_distribution: Dict[int, int]
    complexity_level: str  # "simple", "medium", "complex"


class LayerCompatibilityDataset(Dataset):
    """Custom dataset for layer compatibility testing"""
    
    def __init__(self, features: torch.Tensor, labels: torch.Tensor, 
                 dataset_info: DatasetInfo):
        self.features = features
        self.labels = labels
        self.dataset_info = dataset_info
        
        # Validate dimensions
        assert features.shape[0] == labels.shape[0], "Feature and label count mismatch"
        assert features.shape[1] == dataset_info.num_features, "Feature dimension mismatch"
        
    def __len__(self) -> int:
        return len(self.features)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        return self.features[idx], self.labels[idx]
    
    def get_info(self) -> DatasetInfo:
        return self.dataset_info


class DatasetCreator:
    """Creates and manages datasets for layer compatibility testing"""
    
    def __init__(self, data_dir: str = "data", seed: int = 42):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        self.seed = seed
        
        # Set random seeds for reproducibility
        np.random.seed(seed)
        torch.manual_seed(seed)
        
        self.datasets = {}
        self.scalers = {}
    
    def create_iris_dataset(self) -> Tuple[LayerCompatibilityDataset, LayerCompatibilityDataset]:
        """Create simple Iris dataset (150 samples, 4 features, 3 classes)"""
        logger.info("Creating Iris dataset (Simple complexity)")
        
        # Load Iris dataset
        iris = load_iris()
        X, y = iris.data, iris.target
        
        # Split into train/test
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=self.seed, stratify=y
        )
        
        # Standardize features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train_scaled)
        X_test_tensor = torch.FloatTensor(X_test_scaled)
        y_train_tensor = torch.LongTensor(y_train)
        y_test_tensor = torch.LongTensor(y_test)
        
        # Create dataset info
        class_distribution = {int(k): int(v) for k, v in zip(*np.unique(y_train, return_counts=True))}
        dataset_info = DatasetInfo(
            name="iris",
            num_samples=len(X),
            num_features=X.shape[1],
            num_classes=len(np.unique(y)),
            train_samples=len(X_train),
            test_samples=len(X_test),
            feature_range=(float(X_train_scaled.min()), float(X_train_scaled.max())),
            class_distribution=class_distribution,
            complexity_level="simple"
        )
        
        # Create datasets
        train_dataset = LayerCompatibilityDataset(X_train_tensor, y_train_tensor, dataset_info)
        test_dataset = LayerCompatibilityDataset(X_test_tensor, y_test_tensor, dataset_info)
        
        # Store scaler and datasets
        self.scalers["iris"] = scaler
        self.datasets["iris"] = (train_dataset, test_dataset)
        
        logger.info(f"Iris dataset created: {dataset_info.train_samples} train, {dataset_info.test_samples} test")
        return train_dataset, test_dataset
    
    def create_synthetic_medium_dataset(self) -> Tuple[LayerCompatibilityDataset, LayerCompatibilityDataset]:
        """Create medium synthetic dataset (1000 samples, 8 features, 5 classes)"""
        logger.info("Creating synthetic medium dataset (Medium complexity)")
        
        # Generate synthetic classification data
        X, y = make_classification(
            n_samples=1000,
            n_features=8,
            n_informative=6,
            n_redundant=2,
            n_classes=5,
            n_clusters_per_class=1,
            random_state=self.seed,
            class_sep=0.8
        )
        
        # Split into train/test
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=self.seed, stratify=y
        )
        
        # Standardize features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train_scaled)
        X_test_tensor = torch.FloatTensor(X_test_scaled)
        y_train_tensor = torch.LongTensor(y_train)
        y_test_tensor = torch.LongTensor(y_test)
        
        # Create dataset info
        class_distribution = {int(k): int(v) for k, v in zip(*np.unique(y_train, return_counts=True))}
        dataset_info = DatasetInfo(
            name="synthetic_medium",
            num_samples=len(X),
            num_features=X.shape[1],
            num_classes=len(np.unique(y)),
            train_samples=len(X_train),
            test_samples=len(X_test),
            feature_range=(float(X_train_scaled.min()), float(X_train_scaled.max())),
            class_distribution=class_distribution,
            complexity_level="medium"
        )
        
        # Create datasets
        train_dataset = LayerCompatibilityDataset(X_train_tensor, y_train_tensor, dataset_info)
        test_dataset = LayerCompatibilityDataset(X_test_tensor, y_test_tensor, dataset_info)
        
        # Store scaler and datasets
        self.scalers["synthetic_medium"] = scaler
        self.datasets["synthetic_medium"] = (train_dataset, test_dataset)
        
        logger.info(f"Synthetic medium dataset created: {dataset_info.train_samples} train, {dataset_info.test_samples} test")
        return train_dataset, test_dataset
    
    def create_synthetic_complex_dataset(self) -> Tuple[LayerCompatibilityDataset, LayerCompatibilityDataset]:
        """Create complex synthetic dataset (5000 samples, 12 features, 7 classes)"""
        logger.info("Creating synthetic complex dataset (Complex complexity)")
        
        # Generate complex synthetic classification data
        X, y = make_classification(
            n_samples=5000,
            n_features=12,
            n_informative=10,
            n_redundant=2,
            n_classes=7,
            n_clusters_per_class=2,
            random_state=self.seed,
            class_sep=0.6,
            flip_y=0.01  # Add some noise
        )
        
        # Split into train/test
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=self.seed, stratify=y
        )
        
        # Standardize features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train_scaled)
        X_test_tensor = torch.FloatTensor(X_test_scaled)
        y_train_tensor = torch.LongTensor(y_train)
        y_test_tensor = torch.LongTensor(y_test)
        
        # Create dataset info
        class_distribution = {int(k): int(v) for k, v in zip(*np.unique(y_train, return_counts=True))}
        dataset_info = DatasetInfo(
            name="synthetic_complex",
            num_samples=len(X),
            num_features=X.shape[1],
            num_classes=len(np.unique(y)),
            train_samples=len(X_train),
            test_samples=len(X_test),
            feature_range=(float(X_train_scaled.min()), float(X_train_scaled.max())),
            class_distribution=class_distribution,
            complexity_level="complex"
        )
        
        # Create datasets
        train_dataset = LayerCompatibilityDataset(X_train_tensor, y_train_tensor, dataset_info)
        test_dataset = LayerCompatibilityDataset(X_test_tensor, y_test_tensor, dataset_info)
        
        # Store scaler and datasets
        self.scalers["synthetic_complex"] = scaler
        self.datasets["synthetic_complex"] = (train_dataset, test_dataset)
        
        logger.info(f"Synthetic complex dataset created: {dataset_info.train_samples} train, {dataset_info.test_samples} test")
        return train_dataset, test_dataset
    
    def create_all_datasets(self) -> Dict[str, Tuple[LayerCompatibilityDataset, LayerCompatibilityDataset]]:
        """Create all datasets for the experiment"""
        logger.info("Creating all datasets for layer compatibility testing")
        
        datasets = {}
        datasets["iris"] = self.create_iris_dataset()
        datasets["synthetic_medium"] = self.create_synthetic_medium_dataset()
        datasets["synthetic_complex"] = self.create_synthetic_complex_dataset()
        
        # Save dataset information
        self.save_dataset_info()
        
        return datasets
    
    def save_dataset_info(self) -> None:
        """Save dataset information to JSON file"""
        info_file = self.data_dir / "dataset_info.json"
        
        dataset_info = {}
        for name, (train_dataset, test_dataset) in self.datasets.items():
            info = train_dataset.get_info()
            dataset_info[name] = {
                "name": info.name,
                "num_samples": info.num_samples,
                "num_features": info.num_features,
                "num_classes": info.num_classes,
                "train_samples": info.train_samples,
                "test_samples": info.test_samples,
                "feature_range": info.feature_range,
                "class_distribution": info.class_distribution,
                "complexity_level": info.complexity_level
            }
        
        with open(info_file, 'w') as f:
            json.dump(dataset_info, f, indent=2)
        
        logger.info(f"Dataset information saved to: {info_file}")
    
    def get_dataloader(self, dataset_name: str, split: str = "train", 
                      batch_size: int = 32, shuffle: bool = True) -> DataLoader:
        """Get DataLoader for specified dataset and split"""
        if dataset_name not in self.datasets:
            raise ValueError(f"Dataset {dataset_name} not found. Available: {list(self.datasets.keys())}")
        
        train_dataset, test_dataset = self.datasets[dataset_name]
        dataset = train_dataset if split == "train" else test_dataset
        
        return DataLoader(dataset, batch_size=batch_size, shuffle=shuffle)
    
    def verify_datasets(self) -> Dict[str, bool]:
        """Verify all datasets are correctly created"""
        logger.info("Verifying dataset integrity")
        
        verification_results = {}
        
        for name, (train_dataset, test_dataset) in self.datasets.items():
            try:
                # Check basic properties
                info = train_dataset.get_info()
                
                # Verify train dataset
                assert len(train_dataset) == info.train_samples
                sample_features, sample_labels = train_dataset[0]
                assert sample_features.shape[0] == info.num_features
                assert 0 <= sample_labels.item() < info.num_classes
                
                # Verify test dataset
                assert len(test_dataset) == info.test_samples
                sample_features, sample_labels = test_dataset[0]
                assert sample_features.shape[0] == info.num_features
                assert 0 <= sample_labels.item() < info.num_classes
                
                # Test DataLoader
                train_loader = self.get_dataloader(name, "train", batch_size=16)
                batch_features, batch_labels = next(iter(train_loader))
                assert batch_features.shape[1] == info.num_features
                assert batch_labels.max().item() < info.num_classes
                
                verification_results[name] = True
                logger.info(f"✓ Dataset {name} verified successfully")
                
            except Exception as e:
                verification_results[name] = False
                logger.error(f"✗ Dataset {name} verification failed: {e}")
        
        return verification_results


def create_and_verify_datasets() -> DatasetCreator:
    """Create and verify all datasets for the experiment"""
    print("=== Dataset Creation and Verification ===")
    
    creator = DatasetCreator()
    
    # Create all datasets
    datasets = creator.create_all_datasets()
    
    # Verify datasets
    verification_results = creator.verify_datasets()
    
    # Print summary
    print("\\nDataset Summary:")
    for name, (train_dataset, test_dataset) in datasets.items():
        info = train_dataset.get_info()
        status = "✓" if verification_results[name] else "✗"
        print(f"  {status} {info.name}: {info.num_samples} samples, "
              f"{info.num_features} features, {info.num_classes} classes "
              f"({info.complexity_level})")
    
    return creator


if __name__ == "__main__":
    creator = create_and_verify_datasets()
