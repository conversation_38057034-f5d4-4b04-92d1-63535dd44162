"""
Layer Compatibility Testing Protocol
For Neural Network Layer Compatibility Verification Experiments

This module implements systematic testing for:
- Dimension mismatch detection (compile-time vs runtime)
- Batch size variation testing with timing and memory measurement
- Architecture modification testing
- Input shape validation
- Runtime vs compile-time error detection comparison

Tests are designed to demonstrate the benefits of dependent types
in Idris vs runtime checking in Python implementations.
"""

import torch
import torch.nn as nn
import numpy as np
import time
import traceback
from typing import Dict, List, Any, Tuple, Optional, Callable
from dataclasses import dataclass
from datetime import datetime
import logging

from metrics_collection import get_metrics_logger, SafetyMetrics, RobustnessMetrics
from neural_network import MNISTNetwork
from neural_network_manual_checks import MNISTNetworkManualChecks, DimensionError
from neural_network_shape_annotations import MNISTNetworkShapeAnnotations

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class CompatibilityTestResult:
    """Result of a compatibility test"""
    test_name: str
    implementation_type: str
    expected_to_fail: bool
    actually_failed: bool
    error_type: str
    error_message: str
    detection_time: str  # "compile_time" or "runtime"
    execution_time_ms: float
    memory_usage_mb: float
    timestamp: str


class LayerCompatibilityTester:
    """Comprehensive layer compatibility testing system"""
    
    def __init__(self, log_results: bool = True):
        self.log_results = log_results
        self.metrics_logger = get_metrics_logger()
        self.test_results = []
        
        # Test configurations
        self.batch_sizes = [1, 16, 32, 64, 128, 256]
        self.invalid_input_dims = [783, 785, 800, 1000]  # Wrong input dimensions
        self.invalid_architectures = [
            {"hidden1": 255, "hidden2": 128},  # Wrong hidden1 dim
            {"hidden1": 256, "hidden2": 127},  # Wrong hidden2 dim
            {"hidden1": 100, "hidden2": 50},   # Completely different architecture
        ]
    
    def _time_and_measure(self, func: Callable) -> Tuple[Any, float, float]:
        """Time execution and measure memory usage"""
        import psutil
        process = psutil.Process()
        
        # Measure initial memory
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Time execution
        start_time = time.perf_counter()
        try:
            result = func()
            success = True
            error = None
        except Exception as e:
            result = None
            success = False
            error = e
        end_time = time.perf_counter()
        
        # Measure final memory
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_usage = final_memory - initial_memory
        
        execution_time = (end_time - start_time) * 1000  # Convert to ms
        
        if not success:
            raise error
        
        return result, execution_time, memory_usage
    
    def test_dimension_mismatches(self, model_class, model_name: str) -> List[CompatibilityTestResult]:
        """Test various dimension mismatch scenarios"""
        logger.info(f"Testing dimension mismatches for {model_name}")
        results = []
        
        # Create model
        model = model_class()
        model.eval()
        
        # Test 1: Wrong input feature dimension
        for wrong_dim in self.invalid_input_dims:
            test_name = f"wrong_input_dim_{wrong_dim}"
            
            def test_func():
                invalid_input = torch.randn(32, wrong_dim)
                return model(invalid_input)
            
            try:
                _, exec_time, memory = self._time_and_measure(test_func)
                # If we get here, the test unexpectedly passed
                result = CompatibilityTestResult(
                    test_name=test_name,
                    implementation_type=model_name,
                    expected_to_fail=True,
                    actually_failed=False,
                    error_type="none",
                    error_message="Test unexpectedly passed",
                    detection_time="runtime",
                    execution_time_ms=exec_time,
                    memory_usage_mb=memory,
                    timestamp=datetime.now().isoformat()
                )
            except Exception as e:
                _, exec_time, memory = self._time_and_measure(lambda: None)
                result = CompatibilityTestResult(
                    test_name=test_name,
                    implementation_type=model_name,
                    expected_to_fail=True,
                    actually_failed=True,
                    error_type=type(e).__name__,
                    error_message=str(e),
                    detection_time="runtime",
                    execution_time_ms=exec_time,
                    memory_usage_mb=memory,
                    timestamp=datetime.now().isoformat()
                )
            
            results.append(result)
        
        # Test 2: Wrong tensor dimensionality
        test_name = "wrong_tensor_dimensionality"
        
        def test_func():
            invalid_input = torch.randn(784)  # 1D instead of 2D
            return model(invalid_input)
        
        try:
            _, exec_time, memory = self._time_and_measure(test_func)
            result = CompatibilityTestResult(
                test_name=test_name,
                implementation_type=model_name,
                expected_to_fail=True,
                actually_failed=False,
                error_type="none",
                error_message="Test unexpectedly passed",
                detection_time="runtime",
                execution_time_ms=exec_time,
                memory_usage_mb=memory,
                timestamp=datetime.now().isoformat()
            )
        except Exception as e:
            _, exec_time, memory = self._time_and_measure(lambda: None)
            result = CompatibilityTestResult(
                test_name=test_name,
                implementation_type=model_name,
                expected_to_fail=True,
                actually_failed=True,
                error_type=type(e).__name__,
                error_message=str(e),
                detection_time="runtime",
                execution_time_ms=exec_time,
                memory_usage_mb=memory,
                timestamp=datetime.now().isoformat()
            )
        
        results.append(result)
        
        return results
    
    def test_batch_size_variations(self, model_class, model_name: str) -> List[CompatibilityTestResult]:
        """Test different batch sizes and measure performance scaling"""
        logger.info(f"Testing batch size variations for {model_name}")
        results = []
        
        model = model_class()
        model.eval()
        
        for batch_size in self.batch_sizes:
            test_name = f"batch_size_{batch_size}"
            
            def test_func():
                test_input = torch.randn(batch_size, 784)
                return model(test_input)
            
            try:
                output, exec_time, memory = self._time_and_measure(test_func)
                
                # Verify output shape
                expected_shape = (batch_size, 10)
                actual_shape = output.shape
                
                success = actual_shape == expected_shape
                error_msg = "" if success else f"Shape mismatch: expected {expected_shape}, got {actual_shape}"
                
                result = CompatibilityTestResult(
                    test_name=test_name,
                    implementation_type=model_name,
                    expected_to_fail=False,
                    actually_failed=not success,
                    error_type="shape_mismatch" if not success else "none",
                    error_message=error_msg,
                    detection_time="runtime",
                    execution_time_ms=exec_time,
                    memory_usage_mb=memory,
                    timestamp=datetime.now().isoformat()
                )
                
            except Exception as e:
                _, exec_time, memory = self._time_and_measure(lambda: None)
                result = CompatibilityTestResult(
                    test_name=test_name,
                    implementation_type=model_name,
                    expected_to_fail=False,
                    actually_failed=True,
                    error_type=type(e).__name__,
                    error_message=str(e),
                    detection_time="runtime",
                    execution_time_ms=exec_time,
                    memory_usage_mb=memory,
                    timestamp=datetime.now().isoformat()
                )
            
            results.append(result)
        
        return results
    
    def test_architecture_modifications(self, model_class, model_name: str) -> List[CompatibilityTestResult]:
        """Test incompatible architecture modifications"""
        logger.info(f"Testing architecture modifications for {model_name}")
        results = []
        
        for i, arch_config in enumerate(self.invalid_architectures):
            test_name = f"invalid_architecture_{i}"
            
            def test_func():
                # Try to create model with invalid architecture
                if model_name == "baseline":
                    model = MNISTNetwork(
                        hidden1_dim=arch_config["hidden1"],
                        hidden2_dim=arch_config["hidden2"]
                    )
                elif model_name == "manual_checks":
                    model = MNISTNetworkManualChecks(
                        hidden1_dim=arch_config["hidden1"],
                        hidden2_dim=arch_config["hidden2"]
                    )
                elif model_name == "shape_annotations":
                    model = MNISTNetworkShapeAnnotations(
                        hidden1_dim=arch_config["hidden1"],
                        hidden2_dim=arch_config["hidden2"]
                    )
                
                # Test with standard input
                test_input = torch.randn(32, 784)
                return model(test_input)
            
            try:
                _, exec_time, memory = self._time_and_measure(test_func)
                result = CompatibilityTestResult(
                    test_name=test_name,
                    implementation_type=model_name,
                    expected_to_fail=False,  # These should work, just different architectures
                    actually_failed=False,
                    error_type="none",
                    error_message="",
                    detection_time="runtime",
                    execution_time_ms=exec_time,
                    memory_usage_mb=memory,
                    timestamp=datetime.now().isoformat()
                )
            except Exception as e:
                _, exec_time, memory = self._time_and_measure(lambda: None)
                result = CompatibilityTestResult(
                    test_name=test_name,
                    implementation_type=model_name,
                    expected_to_fail=False,
                    actually_failed=True,
                    error_type=type(e).__name__,
                    error_message=str(e),
                    detection_time="runtime",
                    execution_time_ms=exec_time,
                    memory_usage_mb=memory,
                    timestamp=datetime.now().isoformat()
                )
            
            results.append(result)
        
        return results
    
    def run_comprehensive_tests(self) -> Dict[str, List[CompatibilityTestResult]]:
        """Run comprehensive compatibility tests on all implementations"""
        logger.info("Starting comprehensive layer compatibility tests")
        
        # Define model implementations to test
        implementations = [
            (MNISTNetwork, "baseline"),
            (MNISTNetworkManualChecks, "manual_checks"),
            (MNISTNetworkShapeAnnotations, "shape_annotations")
        ]
        
        all_results = {}
        
        for model_class, model_name in implementations:
            logger.info(f"Testing {model_name} implementation")
            
            implementation_results = []
            
            # Run all test categories
            implementation_results.extend(self.test_dimension_mismatches(model_class, model_name))
            implementation_results.extend(self.test_batch_size_variations(model_class, model_name))
            implementation_results.extend(self.test_architecture_modifications(model_class, model_name))
            
            all_results[model_name] = implementation_results
            
            # Log results if enabled
            if self.log_results:
                self._log_results(implementation_results, model_name)
        
        self.test_results = all_results
        return all_results
    
    def _log_results(self, results: List[CompatibilityTestResult], implementation_type: str):
        """Log test results to metrics system"""
        # Count different types of results
        total_tests = len(results)
        runtime_errors = sum(1 for r in results if r.actually_failed)
        dimension_checks = sum(1 for r in results if "dim" in r.test_name)
        
        # Calculate type safety score
        expected_failures = sum(1 for r in results if r.expected_to_fail)
        correct_predictions = sum(1 for r in results if r.expected_to_fail == r.actually_failed)
        type_safety_score = correct_predictions / max(1, total_tests)
        
        # Create safety metrics
        safety_metrics = SafetyMetrics(
            compile_time_errors=0,  # Python implementations don't have compile-time errors
            runtime_errors=runtime_errors,
            dimension_checks_performed=dimension_checks,
            dimension_errors_caught=runtime_errors,
            type_safety_score=type_safety_score,
            implementation_type=implementation_type,
            timestamp=datetime.now().isoformat()
        )
        
        # Create robustness metrics
        robustness_metrics = RobustnessMetrics(
            dimension_mismatch_tests=sum(1 for r in results if "dim" in r.test_name),
            gradient_stability_score=1.0,  # Placeholder
            architecture_modification_tests=sum(1 for r in results if "architecture" in r.test_name),
            input_validation_tests=sum(1 for r in results if "input" in r.test_name or "tensor" in r.test_name),
            errors_detected_at_runtime=runtime_errors,
            errors_prevented_at_compile_time=0,  # Python implementations
            timestamp=datetime.now().isoformat()
        )
        
        # Log to metrics system
        self.metrics_logger.log_safety(safety_metrics)
        self.metrics_logger.log_robustness(robustness_metrics)
    
    def generate_compatibility_report(self) -> Dict[str, Any]:
        """Generate comprehensive compatibility test report"""
        if not self.test_results:
            return {"error": "No test results available"}
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_implementations_tested": len(self.test_results),
            "implementations": {}
        }
        
        for impl_name, results in self.test_results.items():
            impl_stats = {
                "total_tests": len(results),
                "passed_tests": sum(1 for r in results if not r.actually_failed),
                "failed_tests": sum(1 for r in results if r.actually_failed),
                "expected_failures": sum(1 for r in results if r.expected_to_fail),
                "unexpected_failures": sum(1 for r in results if r.actually_failed and not r.expected_to_fail),
                "avg_execution_time_ms": np.mean([r.execution_time_ms for r in results]),
                "avg_memory_usage_mb": np.mean([r.memory_usage_mb for r in results]),
                "error_types": {}
            }
            
            # Count error types
            for result in results:
                if result.actually_failed:
                    error_type = result.error_type
                    impl_stats["error_types"][error_type] = impl_stats["error_types"].get(error_type, 0) + 1
            
            report["implementations"][impl_name] = impl_stats
        
        return report


def run_layer_compatibility_tests() -> LayerCompatibilityTester:
    """Run comprehensive layer compatibility tests"""
    print("=== Layer Compatibility Testing Protocol ===")
    
    tester = LayerCompatibilityTester(log_results=True)
    
    # Run all tests
    results = tester.run_comprehensive_tests()
    
    # Generate report
    report = tester.generate_compatibility_report()
    
    # Print summary
    print("\\nTest Results Summary:")
    for impl_name, impl_results in results.items():
        total = len(impl_results)
        failed = sum(1 for r in impl_results if r.actually_failed)
        passed = total - failed
        print(f"  {impl_name}: {passed}/{total} tests passed ({failed} failed)")
    
    return tester


if __name__ == "__main__":
    tester = run_layer_compatibility_tests()
