"""
Comprehensive Metrics Collection System
For Neural Network Layer Compatibility Verification Experiments

This module provides detailed logging and monitoring for:
- Performance metrics (accuracy, F1-score, loss)
- Safety metrics (compile-time vs runtime error detection)
- Execution timing (forward/backward pass, training, inference)
- Memory usage (CPU/GPU, peak usage, efficiency)
- Robustness testing (dimension mismatches, gradient stability)
- Type safety benefits analysis

All metrics are logged with timestamps and saved to structured log files.
"""

import time
import psutil
import torch
import numpy as np
import json
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from contextlib import contextmanager
import threading
import gc
from datetime import datetime


@dataclass
class PerformanceMetrics:
    """Performance metrics for model evaluation"""
    accuracy: float
    f1_score: float
    precision: float
    recall: float
    train_loss: float
    val_loss: float
    epoch: int
    timestamp: str


@dataclass
class TimingMetrics:
    """Detailed timing measurements"""
    forward_pass_time: float  # seconds
    backward_pass_time: float  # seconds
    total_batch_time: float  # seconds
    epoch_time: float  # seconds
    inference_time: float  # seconds
    batch_size: int
    timestamp: str


@dataclass
class MemoryMetrics:
    """Memory usage measurements"""
    cpu_memory_mb: float
    gpu_memory_mb: float
    peak_cpu_memory_mb: float
    peak_gpu_memory_mb: float
    memory_efficiency: float  # MB per sample
    batch_size: int
    timestamp: str


@dataclass
class SafetyMetrics:
    """Type safety and error detection metrics"""
    compile_time_errors: int
    runtime_errors: int
    dimension_checks_performed: int
    dimension_errors_caught: int
    type_safety_score: float  # 0-1 scale
    implementation_type: str  # "idris", "manual_checks", "shape_annotations", "baseline"
    timestamp: str


@dataclass
class RobustnessMetrics:
    """Robustness testing results"""
    dimension_mismatch_tests: int
    gradient_stability_score: float
    architecture_modification_tests: int
    input_validation_tests: int
    errors_detected_at_runtime: int
    errors_prevented_at_compile_time: int
    timestamp: str


class MetricsLogger:
    """Centralized metrics logging system"""
    
    def __init__(self, log_dir: str = "logs"):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Set up individual loggers for different metric types
        self.performance_logger = self._setup_logger("performance", "performance.log")
        self.timing_logger = self._setup_logger("timing", "timing.log")
        self.memory_logger = self._setup_logger("memory", "memory.log")
        self.safety_logger = self._setup_logger("safety", "safety.log")
        self.robustness_logger = self._setup_logger("robustness", "robustness.log")
        self.compilation_logger = self._setup_logger("compilation", "compilation_details.log")
        
        # Memory monitoring
        self.memory_monitor_active = False
        self.memory_samples = []
        self.peak_memory = {"cpu": 0, "gpu": 0}
        
        # Timing context stack
        self.timing_stack = []
        
    def _setup_logger(self, name: str, filename: str) -> logging.Logger:
        """Set up individual logger with file handler"""
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        
        # Remove existing handlers to avoid duplicates
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler
        file_handler = logging.FileHandler(self.log_dir / filename)
        file_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        return logger
    
    def log_performance(self, metrics: PerformanceMetrics) -> None:
        """Log performance metrics"""
        self.performance_logger.info(json.dumps(asdict(metrics)))
    
    def log_timing(self, metrics: TimingMetrics) -> None:
        """Log timing metrics"""
        self.timing_logger.info(json.dumps(asdict(metrics)))
    
    def log_memory(self, metrics: MemoryMetrics) -> None:
        """Log memory metrics"""
        self.memory_logger.info(json.dumps(asdict(metrics)))
    
    def log_safety(self, metrics: SafetyMetrics) -> None:
        """Log safety metrics"""
        self.safety_logger.info(json.dumps(asdict(metrics)))
    
    def log_robustness(self, metrics: RobustnessMetrics) -> None:
        """Log robustness metrics"""
        self.robustness_logger.info(json.dumps(asdict(metrics)))
    
    def log_compilation(self, message: str, level: str = "INFO") -> None:
        """Log compilation details"""
        if level.upper() == "ERROR":
            self.compilation_logger.error(message)
        elif level.upper() == "WARNING":
            self.compilation_logger.warning(message)
        else:
            self.compilation_logger.info(message)
    
    @contextmanager
    def time_operation(self, operation_name: str):
        """Context manager for timing operations"""
        start_time = time.perf_counter()
        self.timing_stack.append((operation_name, start_time))
        
        try:
            yield
        finally:
            end_time = time.perf_counter()
            duration = end_time - start_time
            
            # Log the timing
            timing_info = {
                "operation": operation_name,
                "duration_seconds": duration,
                "timestamp": datetime.now().isoformat()
            }
            self.timing_logger.info(json.dumps(timing_info))
            
            # Remove from stack
            self.timing_stack.pop()
    
    def start_memory_monitoring(self):
        """Start continuous memory monitoring"""
        self.memory_monitor_active = True
        self.memory_samples = []
        self.peak_memory = {"cpu": 0, "gpu": 0}
        
        def monitor_memory():
            while self.memory_monitor_active:
                # CPU memory
                process = psutil.Process()
                cpu_memory = process.memory_info().rss / 1024 / 1024  # MB
                
                # GPU memory
                gpu_memory = 0
                if torch.cuda.is_available():
                    gpu_memory = torch.cuda.memory_allocated() / 1024 / 1024  # MB
                
                # Update peaks
                self.peak_memory["cpu"] = max(self.peak_memory["cpu"], cpu_memory)
                self.peak_memory["gpu"] = max(self.peak_memory["gpu"], gpu_memory)
                
                # Store sample
                self.memory_samples.append({
                    "cpu_mb": cpu_memory,
                    "gpu_mb": gpu_memory,
                    "timestamp": time.time()
                })
                
                time.sleep(0.1)  # Sample every 100ms
        
        # Start monitoring thread
        self.monitor_thread = threading.Thread(target=monitor_memory, daemon=True)
        self.monitor_thread.start()
    
    def stop_memory_monitoring(self) -> Dict[str, float]:
        """Stop memory monitoring and return statistics"""
        self.memory_monitor_active = False
        
        if not self.memory_samples:
            return {"cpu_peak": 0, "gpu_peak": 0, "cpu_avg": 0, "gpu_avg": 0}
        
        cpu_values = [sample["cpu_mb"] for sample in self.memory_samples]
        gpu_values = [sample["gpu_mb"] for sample in self.memory_samples]
        
        stats = {
            "cpu_peak": self.peak_memory["cpu"],
            "gpu_peak": self.peak_memory["gpu"],
            "cpu_avg": np.mean(cpu_values),
            "gpu_avg": np.mean(gpu_values),
            "samples_collected": len(self.memory_samples)
        }
        
        return stats
    
    def measure_batch_performance(self, model, inputs, labels, 
                                batch_size: int) -> Tuple[TimingMetrics, MemoryMetrics]:
        """Measure detailed performance for a single batch"""
        # Start memory monitoring
        self.start_memory_monitoring()
        
        # Time forward pass
        start_time = time.perf_counter()
        with torch.no_grad():
            outputs = model(inputs)
        forward_time = time.perf_counter() - start_time
        
        # Time backward pass (if training)
        backward_time = 0
        if model.training:
            start_time = time.perf_counter()
            loss = torch.nn.functional.cross_entropy(outputs, labels)
            loss.backward()
            backward_time = time.perf_counter() - start_time
        
        total_batch_time = forward_time + backward_time
        
        # Stop memory monitoring
        memory_stats = self.stop_memory_monitoring()
        
        # Create metrics
        timing_metrics = TimingMetrics(
            forward_pass_time=forward_time,
            backward_pass_time=backward_time,
            total_batch_time=total_batch_time,
            epoch_time=0,  # Will be set by caller
            inference_time=forward_time,
            batch_size=batch_size,
            timestamp=datetime.now().isoformat()
        )
        
        memory_metrics = MemoryMetrics(
            cpu_memory_mb=memory_stats["cpu_avg"],
            gpu_memory_mb=memory_stats["gpu_avg"],
            peak_cpu_memory_mb=memory_stats["cpu_peak"],
            peak_gpu_memory_mb=memory_stats["gpu_peak"],
            memory_efficiency=memory_stats["cpu_avg"] / batch_size,
            batch_size=batch_size,
            timestamp=datetime.now().isoformat()
        )
        
        return timing_metrics, memory_metrics
    
    def save_summary_report(self, experiment_name: str) -> None:
        """Save comprehensive summary report"""
        summary_file = self.log_dir / f"{experiment_name}_summary.json"
        
        summary = {
            "experiment_name": experiment_name,
            "timestamp": datetime.now().isoformat(),
            "log_files": {
                "performance": str(self.log_dir / "performance.log"),
                "timing": str(self.log_dir / "timing.log"),
                "memory": str(self.log_dir / "memory.log"),
                "safety": str(self.log_dir / "safety.log"),
                "robustness": str(self.log_dir / "robustness.log"),
                "compilation": str(self.log_dir / "compilation_details.log")
            },
            "metrics_collected": {
                "performance_metrics": True,
                "timing_metrics": True,
                "memory_metrics": True,
                "safety_metrics": True,
                "robustness_metrics": True,
                "compilation_logs": True
            }
        }
        
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"Summary report saved to: {summary_file}")


# Global metrics logger instance
metrics_logger = MetricsLogger()


def get_metrics_logger() -> MetricsLogger:
    """Get the global metrics logger instance"""
    return metrics_logger
