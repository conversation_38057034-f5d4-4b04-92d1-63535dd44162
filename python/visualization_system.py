"""
Comprehensive Visualization and Plot Generation System
For Neural Network Layer Compatibility Verification Experiments

Generates all required plots and saves to /plots directory:
- Performance comparison plots (accuracy, loss, convergence)
- Timing analysis plots (execution time, scaling analysis)
- Memory analysis plots (usage, efficiency, peak memory)
- Type safety plots (error detection, prevention effectiveness)
- Architecture visualization (network diagrams, dimension flow)
- Performance vs Safety trade-off analysis

All plots are automatically generated and saved with consistent styling.
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import json
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
import logging
from datetime import datetime

# Set up plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VisualizationSystem:
    """Comprehensive visualization system for experiment results"""
    
    def __init__(self, plots_dir: str = "plots", logs_dir: str = "logs"):
        self.plots_dir = Path(plots_dir)
        self.logs_dir = Path(logs_dir)
        
        # Create plots directory
        self.plots_dir.mkdir(exist_ok=True)
        
        # Color scheme for implementations
        self.colors = {
            'idris': '#2E86AB',
            'manual_checks': '#A23B72',
            'shape_annotations': '#F18F01',
            'baseline': '#C73E1D'
        }
        
        # Plot configurations
        self.figure_size = (12, 8)
        self.dpi = 300
        
    def load_metrics_data(self) -> Dict[str, Any]:
        """Load all metrics data from log files"""
        metrics_data = {
            'performance': [],
            'timing': [],
            'memory': [],
            'safety': [],
            'robustness': []
        }
        
        # Load each type of metrics
        for metric_type in metrics_data.keys():
            log_file = self.logs_dir / f"{metric_type}.log"
            if log_file.exists():
                with open(log_file, 'r') as f:
                    for line in f:
                        if line.strip():
                            try:
                                # Parse log line to extract JSON
                                json_part = line.split(' - ')[-1]
                                data = json.loads(json_part)
                                metrics_data[metric_type].append(data)
                            except (json.JSONDecodeError, IndexError):
                                continue
        
        return metrics_data
    
    def create_performance_comparison_plots(self, metrics_data: Dict[str, Any]) -> None:
        """Create performance comparison plots"""
        logger.info("Creating performance comparison plots")
        
        if not metrics_data['performance']:
            logger.warning("No performance data available")
            return
        
        # Convert to DataFrame
        df = pd.DataFrame(metrics_data['performance'])
        
        # Create subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Performance Comparison Across Implementations', fontsize=16, fontweight='bold')
        
        # Plot 1: Accuracy comparison
        if 'accuracy' in df.columns:
            implementations = df.groupby('implementation_type')['accuracy'].mean() if 'implementation_type' in df.columns else df['accuracy']
            axes[0, 0].bar(range(len(implementations)), implementations.values, 
                          color=[self.colors.get(impl, '#333333') for impl in implementations.index])
            axes[0, 0].set_title('Final Accuracy Comparison')
            axes[0, 0].set_ylabel('Accuracy')
            axes[0, 0].set_xticks(range(len(implementations)))
            axes[0, 0].set_xticklabels(implementations.index, rotation=45)
        
        # Plot 2: Loss comparison
        if 'train_loss' in df.columns and 'val_loss' in df.columns:
            x = np.arange(len(df))
            axes[0, 1].plot(x, df['train_loss'], label='Training Loss', alpha=0.7)
            axes[0, 1].plot(x, df['val_loss'], label='Validation Loss', alpha=0.7)
            axes[0, 1].set_title('Training and Validation Loss')
            axes[0, 1].set_ylabel('Loss')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].legend()
        
        # Plot 3: F1 Score comparison
        if 'f1_score' in df.columns:
            implementations = df.groupby('implementation_type')['f1_score'].mean() if 'implementation_type' in df.columns else df['f1_score']
            axes[1, 0].bar(range(len(implementations)), implementations.values,
                          color=[self.colors.get(impl, '#333333') for impl in implementations.index])
            axes[1, 0].set_title('F1 Score Comparison')
            axes[1, 0].set_ylabel('F1 Score')
            axes[1, 0].set_xticks(range(len(implementations)))
            axes[1, 0].set_xticklabels(implementations.index, rotation=45)
        
        # Plot 4: Convergence speed
        if 'epoch' in df.columns and 'accuracy' in df.columns:
            for impl in df['implementation_type'].unique() if 'implementation_type' in df.columns else ['all']:
                if impl != 'all':
                    impl_data = df[df['implementation_type'] == impl]
                else:
                    impl_data = df
                axes[1, 1].plot(impl_data['epoch'], impl_data['accuracy'], 
                               label=impl, color=self.colors.get(impl, '#333333'))
            axes[1, 1].set_title('Convergence Speed')
            axes[1, 1].set_ylabel('Accuracy')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].legend()
        
        plt.tight_layout()
        plt.savefig(self.plots_dir / 'performance_comparison.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
    
    def create_timing_analysis_plots(self, metrics_data: Dict[str, Any]) -> None:
        """Create timing analysis plots"""
        logger.info("Creating timing analysis plots")
        
        if not metrics_data['timing']:
            logger.warning("No timing data available")
            return
        
        df = pd.DataFrame(metrics_data['timing'])
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Timing Analysis Across Implementations', fontsize=16, fontweight='bold')
        
        # Plot 1: Forward pass timing
        if 'forward_pass_time' in df.columns:
            implementations = df.groupby('implementation_type')['forward_pass_time'].mean() if 'implementation_type' in df.columns else df['forward_pass_time']
            axes[0, 0].bar(range(len(implementations)), implementations.values * 1000,  # Convert to ms
                          color=[self.colors.get(impl, '#333333') for impl in implementations.index])
            axes[0, 0].set_title('Forward Pass Timing')
            axes[0, 0].set_ylabel('Time (ms)')
            axes[0, 0].set_xticks(range(len(implementations)))
            axes[0, 0].set_xticklabels(implementations.index, rotation=45)
        
        # Plot 2: Batch size scaling
        if 'batch_size' in df.columns and 'total_batch_time' in df.columns:
            for impl in df['implementation_type'].unique() if 'implementation_type' in df.columns else ['all']:
                if impl != 'all':
                    impl_data = df[df['implementation_type'] == impl]
                else:
                    impl_data = df
                axes[0, 1].scatter(impl_data['batch_size'], impl_data['total_batch_time'] * 1000,
                                 label=impl, color=self.colors.get(impl, '#333333'), alpha=0.7)
            axes[0, 1].set_title('Batch Size Scaling')
            axes[0, 1].set_ylabel('Time (ms)')
            axes[0, 1].set_xlabel('Batch Size')
            axes[0, 1].legend()
            axes[0, 1].set_xscale('log')
        
        # Plot 3: Backward pass timing
        if 'backward_pass_time' in df.columns:
            implementations = df.groupby('implementation_type')['backward_pass_time'].mean() if 'implementation_type' in df.columns else df['backward_pass_time']
            axes[1, 0].bar(range(len(implementations)), implementations.values * 1000,
                          color=[self.colors.get(impl, '#333333') for impl in implementations.index])
            axes[1, 0].set_title('Backward Pass Timing')
            axes[1, 0].set_ylabel('Time (ms)')
            axes[1, 0].set_xticks(range(len(implementations)))
            axes[1, 0].set_xticklabels(implementations.index, rotation=45)
        
        # Plot 4: Total epoch timing
        if 'epoch_time' in df.columns:
            implementations = df.groupby('implementation_type')['epoch_time'].mean() if 'implementation_type' in df.columns else df['epoch_time']
            axes[1, 1].bar(range(len(implementations)), implementations.values,
                          color=[self.colors.get(impl, '#333333') for impl in implementations.index])
            axes[1, 1].set_title('Epoch Timing')
            axes[1, 1].set_ylabel('Time (s)')
            axes[1, 1].set_xticks(range(len(implementations)))
            axes[1, 1].set_xticklabels(implementations.index, rotation=45)
        
        plt.tight_layout()
        plt.savefig(self.plots_dir / 'timing_analysis.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
    
    def create_memory_analysis_plots(self, metrics_data: Dict[str, Any]) -> None:
        """Create memory analysis plots"""
        logger.info("Creating memory analysis plots")
        
        if not metrics_data['memory']:
            logger.warning("No memory data available")
            return
        
        df = pd.DataFrame(metrics_data['memory'])
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Memory Analysis Across Implementations', fontsize=16, fontweight='bold')
        
        # Plot 1: Peak CPU memory
        if 'peak_cpu_memory_mb' in df.columns:
            implementations = df.groupby('implementation_type')['peak_cpu_memory_mb'].max() if 'implementation_type' in df.columns else df['peak_cpu_memory_mb']
            axes[0, 0].bar(range(len(implementations)), implementations.values,
                          color=[self.colors.get(impl, '#333333') for impl in implementations.index])
            axes[0, 0].set_title('Peak CPU Memory Usage')
            axes[0, 0].set_ylabel('Memory (MB)')
            axes[0, 0].set_xticks(range(len(implementations)))
            axes[0, 0].set_xticklabels(implementations.index, rotation=45)
        
        # Plot 2: Memory efficiency vs batch size
        if 'batch_size' in df.columns and 'memory_efficiency' in df.columns:
            for impl in df['implementation_type'].unique() if 'implementation_type' in df.columns else ['all']:
                if impl != 'all':
                    impl_data = df[df['implementation_type'] == impl]
                else:
                    impl_data = df
                axes[0, 1].scatter(impl_data['batch_size'], impl_data['memory_efficiency'],
                                 label=impl, color=self.colors.get(impl, '#333333'), alpha=0.7)
            axes[0, 1].set_title('Memory Efficiency vs Batch Size')
            axes[0, 1].set_ylabel('Memory per Sample (MB)')
            axes[0, 1].set_xlabel('Batch Size')
            axes[0, 1].legend()
        
        # Plot 3: GPU memory usage (if available)
        if 'gpu_memory_mb' in df.columns:
            implementations = df.groupby('implementation_type')['gpu_memory_mb'].mean() if 'implementation_type' in df.columns else df['gpu_memory_mb']
            axes[1, 0].bar(range(len(implementations)), implementations.values,
                          color=[self.colors.get(impl, '#333333') for impl in implementations.index])
            axes[1, 0].set_title('Average GPU Memory Usage')
            axes[1, 0].set_ylabel('Memory (MB)')
            axes[1, 0].set_xticks(range(len(implementations)))
            axes[1, 0].set_xticklabels(implementations.index, rotation=45)
        
        # Plot 4: Memory usage comparison
        if 'cpu_memory_mb' in df.columns:
            implementations = df.groupby('implementation_type')['cpu_memory_mb'].mean() if 'implementation_type' in df.columns else df['cpu_memory_mb']
            axes[1, 1].bar(range(len(implementations)), implementations.values,
                          color=[self.colors.get(impl, '#333333') for impl in implementations.index])
            axes[1, 1].set_title('Average CPU Memory Usage')
            axes[1, 1].set_ylabel('Memory (MB)')
            axes[1, 1].set_xticks(range(len(implementations)))
            axes[1, 1].set_xticklabels(implementations.index, rotation=45)
        
        plt.tight_layout()
        plt.savefig(self.plots_dir / 'memory_analysis.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
    
    def create_type_safety_plots(self, metrics_data: Dict[str, Any]) -> None:
        """Create type safety analysis plots"""
        logger.info("Creating type safety plots")
        
        if not metrics_data['safety']:
            logger.warning("No safety data available")
            return
        
        df = pd.DataFrame(metrics_data['safety'])
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Type Safety Analysis', fontsize=16, fontweight='bold')
        
        # Plot 1: Compile-time vs Runtime errors
        if 'compile_time_errors' in df.columns and 'runtime_errors' in df.columns:
            implementations = df['implementation_type'].unique() if 'implementation_type' in df.columns else ['all']
            compile_errors = [df[df['implementation_type'] == impl]['compile_time_errors'].sum() if impl != 'all' else df['compile_time_errors'].sum() for impl in implementations]
            runtime_errors = [df[df['implementation_type'] == impl]['runtime_errors'].sum() if impl != 'all' else df['runtime_errors'].sum() for impl in implementations]
            
            x = np.arange(len(implementations))
            width = 0.35
            
            axes[0, 0].bar(x - width/2, compile_errors, width, label='Compile-time', alpha=0.8)
            axes[0, 0].bar(x + width/2, runtime_errors, width, label='Runtime', alpha=0.8)
            axes[0, 0].set_title('Error Detection: Compile-time vs Runtime')
            axes[0, 0].set_ylabel('Number of Errors')
            axes[0, 0].set_xticks(x)
            axes[0, 0].set_xticklabels(implementations, rotation=45)
            axes[0, 0].legend()
        
        # Plot 2: Type safety scores
        if 'type_safety_score' in df.columns:
            implementations = df.groupby('implementation_type')['type_safety_score'].mean() if 'implementation_type' in df.columns else df['type_safety_score']
            axes[0, 1].bar(range(len(implementations)), implementations.values,
                          color=[self.colors.get(impl, '#333333') for impl in implementations.index])
            axes[0, 1].set_title('Type Safety Effectiveness')
            axes[0, 1].set_ylabel('Safety Score (0-1)')
            axes[0, 1].set_xticks(range(len(implementations)))
            axes[0, 1].set_xticklabels(implementations.index, rotation=45)
            axes[0, 1].set_ylim(0, 1)
        
        # Plot 3: Dimension checks performed
        if 'dimension_checks_performed' in df.columns:
            implementations = df.groupby('implementation_type')['dimension_checks_performed'].sum() if 'implementation_type' in df.columns else df['dimension_checks_performed']
            axes[1, 0].bar(range(len(implementations)), implementations.values,
                          color=[self.colors.get(impl, '#333333') for impl in implementations.index])
            axes[1, 0].set_title('Dimension Checks Performed')
            axes[1, 0].set_ylabel('Number of Checks')
            axes[1, 0].set_xticks(range(len(implementations)))
            axes[1, 0].set_xticklabels(implementations.index, rotation=45)
        
        # Plot 4: Error prevention effectiveness
        if 'dimension_errors_caught' in df.columns and 'dimension_checks_performed' in df.columns:
            implementations = df['implementation_type'].unique() if 'implementation_type' in df.columns else ['all']
            effectiveness = []
            for impl in implementations:
                if impl != 'all':
                    impl_data = df[df['implementation_type'] == impl]
                else:
                    impl_data = df
                total_checks = impl_data['dimension_checks_performed'].sum()
                errors_caught = impl_data['dimension_errors_caught'].sum()
                eff = errors_caught / max(1, total_checks)
                effectiveness.append(eff)
            
            axes[1, 1].bar(range(len(implementations)), effectiveness,
                          color=[self.colors.get(impl, '#333333') for impl in implementations])
            axes[1, 1].set_title('Error Prevention Effectiveness')
            axes[1, 1].set_ylabel('Errors Caught / Total Checks')
            axes[1, 1].set_xticks(range(len(implementations)))
            axes[1, 1].set_xticklabels(implementations, rotation=45)
        
        plt.tight_layout()
        plt.savefig(self.plots_dir / 'type_safety_analysis.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
    
    def create_architecture_visualization(self) -> None:
        """Create network architecture visualization"""
        logger.info("Creating architecture visualization")
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        
        # Define layer positions and sizes
        layers = [
            {"name": "Input", "size": 784, "pos": (1, 0.5), "color": "#E8F4FD"},
            {"name": "Hidden 1", "size": 256, "pos": (3, 0.5), "color": "#B8E6B8"},
            {"name": "Hidden 2", "size": 128, "pos": (5, 0.5), "color": "#FFB347"},
            {"name": "Output", "size": 10, "pos": (7, 0.5), "color": "#FFB6C1"}
        ]
        
        # Draw layers
        for i, layer in enumerate(layers):
            # Draw layer rectangle
            rect_height = 0.6
            rect_width = 0.8
            rect = plt.Rectangle((layer["pos"][0] - rect_width/2, layer["pos"][1] - rect_height/2),
                               rect_width, rect_height, facecolor=layer["color"], 
                               edgecolor='black', linewidth=2)
            ax.add_patch(rect)
            
            # Add layer label
            ax.text(layer["pos"][0], layer["pos"][1] + 0.1, layer["name"], 
                   ha='center', va='center', fontsize=12, fontweight='bold')
            ax.text(layer["pos"][0], layer["pos"][1] - 0.1, f"{layer['size']} units", 
                   ha='center', va='center', fontsize=10)
            
            # Draw connections to next layer
            if i < len(layers) - 1:
                next_layer = layers[i + 1]
                ax.arrow(layer["pos"][0] + rect_width/2, layer["pos"][1],
                        next_layer["pos"][0] - layer["pos"][0] - rect_width, 0,
                        head_width=0.05, head_length=0.1, fc='black', ec='black')
        
        # Add dimension annotations
        ax.text(2, 0.9, "784 → 256", ha='center', va='center', fontsize=10, 
               bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
        ax.text(4, 0.9, "256 → 128", ha='center', va='center', fontsize=10,
               bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
        ax.text(6, 0.9, "128 → 10", ha='center', va='center', fontsize=10,
               bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
        
        ax.set_xlim(0, 8)
        ax.set_ylim(0, 1)
        ax.set_aspect('equal')
        ax.axis('off')
        ax.set_title('Neural Network Architecture with Type-Safe Dimensions', 
                    fontsize=16, fontweight='bold', pad=20)
        
        plt.tight_layout()
        plt.savefig(self.plots_dir / 'architecture_visualization.png', dpi=self.dpi, bbox_inches='tight')
        plt.close()
    
    def generate_all_plots(self) -> None:
        """Generate all required plots"""
        logger.info("Generating all visualization plots")
        
        # Load metrics data
        metrics_data = self.load_metrics_data()
        
        # Generate all plot types
        self.create_performance_comparison_plots(metrics_data)
        self.create_timing_analysis_plots(metrics_data)
        self.create_memory_analysis_plots(metrics_data)
        self.create_type_safety_plots(metrics_data)
        self.create_architecture_visualization()
        
        logger.info(f"All plots generated and saved to {self.plots_dir}")
        
        # Create index file
        self._create_plots_index()
    
    def _create_plots_index(self) -> None:
        """Create an index file listing all generated plots"""
        index_file = self.plots_dir / "plots_index.md"
        
        plots = [
            ("performance_comparison.png", "Performance Comparison Across Implementations"),
            ("timing_analysis.png", "Timing Analysis and Scaling"),
            ("memory_analysis.png", "Memory Usage Analysis"),
            ("type_safety_analysis.png", "Type Safety and Error Detection"),
            ("architecture_visualization.png", "Network Architecture Visualization")
        ]
        
        with open(index_file, 'w') as f:
            f.write("# Generated Plots Index\n\n")
            f.write(f"Generated on: {datetime.now().isoformat()}\n\n")
            
            for filename, description in plots:
                if (self.plots_dir / filename).exists():
                    f.write(f"- **{description}**: `{filename}`\n")
                else:
                    f.write(f"- **{description}**: `{filename}` (NOT GENERATED)\n")
        
        logger.info(f"Plots index created: {index_file}")


def generate_all_visualizations() -> VisualizationSystem:
    """Generate all required visualizations"""
    print("=== Comprehensive Visualization Generation ===")
    
    viz_system = VisualizationSystem()
    viz_system.generate_all_plots()
    
    print(f"All plots generated and saved to: {viz_system.plots_dir}")
    return viz_system


if __name__ == "__main__":
    viz_system = generate_all_visualizations()
