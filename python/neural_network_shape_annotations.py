"""
Neural Network implementation with SHAPE ANNOTATIONS and Enhanced Validation
MNIST Architecture: 784 -> 256 -> 128 -> 10

This variant uses comprehensive shape comments, type annotations, and
runtime monitoring of tensor shapes during training. Demonstrates
enhanced shape safety through annotations and validation hooks.

Features:
- Comprehensive shape type annotations using torchtyping
- Custom hooks for dimension validation during training
- Runtime monitoring of tensor shapes
- Shape-aware debugging and logging
- Type hints for all tensor operations

Part of comparison: Idris dependent types vs Python runtime checking.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, List, Optional, Dict, Any, Union
import numpy as np
import time
import logging
from pathlib import Path

# Enhanced type annotations for shapes
try:
    from torchtyping import TensorType, patch_typeguard
    from typeguard import typechecked
    patch_typeguard()
    TORCHTYPING_AVAILABLE = True
except ImportError:
    # Fallback type definitions if torchtyping not available
    TensorType = torch.Tensor
    def typechecked(func):
        return func
    TORCHTYPING_AVAILABLE = False

# Type aliases for clarity
BatchSize = int
InputDim = int  # 784
Hidden1Dim = int  # 256
Hidden2Dim = int  # 128
OutputDim = int  # 10

# Shape type definitions
if TORCHTYPING_AVAILABLE:
    InputTensor = TensorType["batch", 784]
    Hidden1Tensor = TensorType["batch", 256]
    Hidden2Tensor = TensorType["batch", 128]
    OutputTensor = TensorType["batch", 10]
    LabelTensor = TensorType["batch"]
else:
    InputTensor = torch.Tensor
    Hidden1Tensor = torch.Tensor
    Hidden2Tensor = torch.Tensor
    OutputTensor = torch.Tensor
    LabelTensor = torch.Tensor

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ShapeValidationHook:
    """Hook for monitoring tensor shapes during forward/backward passes"""
    
    def __init__(self, layer_name: str, expected_input_shape: Tuple[int, ...], 
                 expected_output_shape: Tuple[int, ...]):
        self.layer_name = layer_name
        self.expected_input_shape = expected_input_shape
        self.expected_output_shape = expected_output_shape
        self.validation_count = 0
        self.errors_detected = 0
    
    def __call__(self, module, input_tensor, output_tensor):
        """Hook function called during forward pass"""
        self.validation_count += 1
        
        # Validate input shape (excluding batch dimension)
        input_shape = input_tensor[0].shape[1:] if isinstance(input_tensor, tuple) else input_tensor.shape[1:]
        if input_shape != self.expected_input_shape[1:]:
            self.errors_detected += 1
            logger.error(f"{self.layer_name} input shape mismatch: "
                        f"expected {self.expected_input_shape[1:]}, got {input_shape}")
        
        # Validate output shape (excluding batch dimension)
        output_shape = output_tensor.shape[1:]
        if output_shape != self.expected_output_shape[1:]:
            self.errors_detected += 1
            logger.error(f"{self.layer_name} output shape mismatch: "
                        f"expected {self.expected_output_shape[1:]}, got {output_shape}")
        
        # Log successful validation
        if self.validation_count % 100 == 0:  # Log every 100 validations
            logger.info(f"{self.layer_name}: {self.validation_count} validations, "
                       f"{self.errors_detected} errors")


class MNISTNetworkShapeAnnotations(nn.Module):
    """Neural Network with comprehensive shape annotations and validation
    
    This implementation uses type annotations, shape comments, and runtime
    hooks to provide enhanced shape safety without compile-time guarantees.
    Demonstrates best practices for shape-aware PyTorch development.
    """

    def __init__(self,
                 input_dim: int = 784,    # 28x28 pixels
                 hidden1_dim: int = 256,  # first hidden layer
                 hidden2_dim: int = 128,  # second hidden layer
                 output_dim: int = 10,    # 10 digit classes
                 enable_shape_hooks: bool = True):
        super(MNISTNetworkShapeAnnotations, self).__init__()
        
        # Store dimensions with type annotations
        self.input_dim: InputDim = input_dim
        self.hidden1_dim: Hidden1Dim = hidden1_dim
        self.hidden2_dim: Hidden2Dim = hidden2_dim
        self.output_dim: OutputDim = output_dim
        
        # Layer definitions with explicit shape documentation
        # Shape: [batch, 784] -> [batch, 256]
        self.layer1: nn.Linear = nn.Linear(input_dim, hidden1_dim)
        
        # Shape: [batch, 256] -> [batch, 128]
        self.layer2: nn.Linear = nn.Linear(hidden1_dim, hidden2_dim)
        
        # Shape: [batch, 128] -> [batch, 10]
        self.layer3: nn.Linear = nn.Linear(hidden2_dim, output_dim)
        
        # Initialize weights
        self._initialize_weights()
        
        # Set up shape validation hooks if enabled
        if enable_shape_hooks:
            self._setup_shape_hooks()
        
        # Track shape validation statistics
        self.shape_validations_performed = 0
        self.shape_errors_detected = 0
    
    def _initialize_weights(self) -> None:
        """Initialize weights using Xavier initialization"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.zeros_(module.bias)
    
    def _setup_shape_hooks(self) -> None:
        """Set up forward hooks for shape validation"""
        # Hook for layer1: [batch, 784] -> [batch, 256]
        hook1 = ShapeValidationHook("layer1", (-1, 784), (-1, 256))
        self.layer1.register_forward_hook(hook1)
        
        # Hook for layer2: [batch, 256] -> [batch, 128]
        hook2 = ShapeValidationHook("layer2", (-1, 256), (-1, 128))
        self.layer2.register_forward_hook(hook2)
        
        # Hook for layer3: [batch, 128] -> [batch, 10]
        hook3 = ShapeValidationHook("layer3", (-1, 128), (-1, 10))
        self.layer3.register_forward_hook(hook3)
        
        # Store hooks for statistics
        self.shape_hooks = [hook1, hook2, hook3]

    @typechecked
    def forward(self, x: InputTensor) -> OutputTensor:
        """
        Forward pass with comprehensive shape annotations
        
        Args:
            x: Input tensor of shape [batch_size, 784]
            
        Returns:
            Output tensor of shape [batch_size, 10]
            
        Shape flow:
            [batch, 784] -> [batch, 256] -> [batch, 128] -> [batch, 10]
        """
        # Input validation with shape annotation
        assert x.shape[-1] == self.input_dim, f"Expected input dim {self.input_dim}, got {x.shape[-1]}"
        
        # Layer 1: [batch, 784] -> [batch, 256]
        h1: Hidden1Tensor = self.layer1(x)
        h1 = F.relu(h1)  # Shape preserved: [batch, 256]
        
        # Layer 2: [batch, 256] -> [batch, 128]
        h2: Hidden2Tensor = self.layer2(h1)
        h2 = F.relu(h2)  # Shape preserved: [batch, 128]
        
        # Layer 3: [batch, 128] -> [batch, 10]
        output: OutputTensor = self.layer3(h2)
        
        # Output validation
        assert output.shape[-1] == self.output_dim, f"Expected output dim {self.output_dim}, got {output.shape[-1]}"
        
        return output

    @typechecked
    def predict(self, x: InputTensor) -> LabelTensor:
        """
        Make prediction with shape annotations
        
        Args:
            x: Input tensor of shape [batch_size, 784] or [784]
            
        Returns:
            Predicted class indices of shape [batch_size]
        """
        if x.dim() == 1:
            x = x.unsqueeze(0)  # [784] -> [1, 784]
        
        with torch.no_grad():
            output: OutputTensor = self.forward(x)  # [batch, 10]
            predictions: LabelTensor = torch.argmax(output, dim=1)  # [batch]
            return predictions

    def get_shape_validation_stats(self) -> Dict[str, Any]:
        """Get comprehensive shape validation statistics"""
        if not hasattr(self, 'shape_hooks'):
            return {"hooks_enabled": False}
        
        total_validations = sum(hook.validation_count for hook in self.shape_hooks)
        total_errors = sum(hook.errors_detected for hook in self.shape_hooks)
        
        hook_stats = {}
        for i, hook in enumerate(self.shape_hooks):
            hook_stats[f"layer{i+1}"] = {
                "validations": hook.validation_count,
                "errors": hook.errors_detected,
                "success_rate": (hook.validation_count - hook.errors_detected) / max(1, hook.validation_count)
            }
        
        return {
            "hooks_enabled": True,
            "total_validations": total_validations,
            "total_errors": total_errors,
            "overall_success_rate": (total_validations - total_errors) / max(1, total_validations),
            "per_layer_stats": hook_stats
        }

    def get_layer_shapes_info(self) -> Dict[str, Dict[str, Tuple[int, ...]]]:
        """Get detailed information about layer shapes"""
        return {
            'layer1': {
                'weight_shape': tuple(self.layer1.weight.shape),
                'bias_shape': tuple(self.layer1.bias.shape),
                'input_shape': (-1, self.input_dim),
                'output_shape': (-1, self.hidden1_dim)
            },
            'layer2': {
                'weight_shape': tuple(self.layer2.weight.shape),
                'bias_shape': tuple(self.layer2.bias.shape),
                'input_shape': (-1, self.hidden1_dim),
                'output_shape': (-1, self.hidden2_dim)
            },
            'layer3': {
                'weight_shape': tuple(self.layer3.weight.shape),
                'bias_shape': tuple(self.layer3.bias.shape),
                'input_shape': (-1, self.hidden2_dim),
                'output_shape': (-1, self.output_dim)
            }
        }


def demonstrate_shape_annotations():
    """Demonstrate shape annotation and validation capabilities"""
    print("=== Shape Annotations and Validation Demonstration ===")
    print(f"TorchTyping available: {TORCHTYPING_AVAILABLE}")
    
    # Create model with shape hooks enabled
    model = MNISTNetworkShapeAnnotations(enable_shape_hooks=True)
    
    print("\\n1. Valid input test with shape monitoring:")
    try:
        valid_input = torch.randn(32, 784)  # Correct shape
        output = model(valid_input)
        print(f"✓ Success: {valid_input.shape} -> {output.shape}")
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
    
    print("\\n2. Shape validation statistics:")
    stats = model.get_shape_validation_stats()
    if stats["hooks_enabled"]:
        print(f"  Total validations: {stats['total_validations']}")
        print(f"  Total errors: {stats['total_errors']}")
        print(f"  Success rate: {stats['overall_success_rate']:.2%}")
    
    print("\\n3. Layer shape information:")
    shapes_info = model.get_layer_shapes_info()
    for layer_name, info in shapes_info.items():
        print(f"  {layer_name}:")
        print(f"    Input: {info['input_shape']} -> Output: {info['output_shape']}")
        print(f"    Weight: {info['weight_shape']}, Bias: {info['bias_shape']}")


if __name__ == "__main__":
    demonstrate_shape_annotations()
