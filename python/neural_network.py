"""
Neural Network implementation using PyTorch - BASELINE VERSION
MNIST Architecture: 784 -> 256 -> 128 -> 10

This is the baseline PyTorch implementation without explicit dimension checking.
Relies on framework's built-in error detection.
Serves as performance baseline for comparison with type-safe implementations.

Part of comprehensive comparison: Idris dependent types vs Python runtime checking.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, List, Optional, Dict, Any
import numpy as np
import time
from pathlib import Path
# import matplotlib.pyplot as plt  # for loss curves, maybe later


class MNISTNetwork(nn.Module):
    """Neural Network for MNIST classification
    Architecture: 784 -> 256 -> 128 -> 10

    Pretty standard architecture. Saw this in multiple tutorials.
    No compile-time checks - just hope the dimensions work out!

    TODO: Add dropout for regularization?
    TODO: Try different activation functions?
    """

    def __init__(self,
                 input_dim: int = 784,    # 28x28 pixels
                 hidden1_dim: int = 256,  # first hidden layer
                 hidden2_dim: int = 128,  # second hidden layer
                 output_dim: int = 10):   # 10 digit classes
        super(MNISTNetwork, self).__init__()
        
        # Layer definitions - dimensions must be manually coordinated
        self.layer1 = nn.Linear(input_dim, hidden1_dim)   # 784 -> 256
        self.layer2 = nn.Linear(hidden1_dim, hidden2_dim) # 256 -> 128  
        self.layer3 = nn.Linear(hidden2_dim, output_dim)  # 128 -> 10
        
        # Store dimensions for debugging
        self.input_dim = input_dim
        self.hidden1_dim = hidden1_dim
        self.hidden2_dim = hidden2_dim
        self.output_dim = output_dim
        
        # Initialize weights using Xavier initialization
        self._initialize_weights()
    
    def _initialize_weights(self) -> None:
        """Initialize weights using Xavier initialization to match Idris version"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.zeros_(module.bias)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the network
        
        Args:
            x: Input tensor of shape [batch_size, 784]
            
        Returns:
            Output tensor of shape [batch_size, 10]
            
        Note: Shape mismatches only caught at runtime!
        """
        # Input validation (runtime check)
        if x.shape[-1] != self.input_dim:
            raise ValueError(f"Input dimension mismatch: expected {self.input_dim}, got {x.shape[-1]}")
        
        # Layer 1: 784 -> 256
        h1 = F.relu(self.layer1(x))        # [batch_size, 256]
        
        # Layer 2: 256 -> 128  
        h2 = F.relu(self.layer2(h1))       # [batch_size, 128]
        
        # Layer 3: 128 -> 10 (no activation on output)
        output = self.layer3(h2)           # [batch_size, 10]
        
        return output
    
    def predict(self, x: torch.Tensor) -> torch.Tensor:
        """
        Make prediction for input
        
        Args:
            x: Input tensor of shape [batch_size, 784] or [784]
            
        Returns:
            Predicted class indices
        """
        if x.dim() == 1:
            x = x.unsqueeze(0)  # Add batch dimension
        
        with torch.no_grad():
            output = self.forward(x)
            return torch.argmax(output, dim=1)
    
    def get_layer_shapes(self) -> Dict[str, Tuple[int, ...]]:
        """Get shapes of all layers for debugging"""
        return {
            'layer1': (self.layer1.weight.shape, self.layer1.bias.shape),
            'layer2': (self.layer2.weight.shape, self.layer2.bias.shape),
            'layer3': (self.layer3.weight.shape, self.layer3.bias.shape),
        }


class NetworkTrainer:
    """Training utilities for the neural network"""
    
    def __init__(self, 
                 model: MNISTNetwork,
                 learning_rate: float = 0.01,
                 device: Optional[torch.device] = None):
        self.model = model
        self.device = device or torch.device('cpu')
        self.model.to(self.device)
        
        # Optimizer and loss function
        self.optimizer = torch.optim.SGD(model.parameters(), lr=learning_rate)
        self.criterion = nn.CrossEntropyLoss()
        
        # Training statistics
        self.epoch_stats: List[Dict[str, Any]] = []
    
    def train_batch(self, 
                   inputs: torch.Tensor, 
                   labels: torch.Tensor) -> Tuple[float, float]:
        """
        Train on a single batch
        
        Args:
            inputs: Input tensor [batch_size, 784]
            labels: Label tensor [batch_size]
            
        Returns:
            (loss, accuracy) for this batch
        """
        self.model.train()
        
        # Move to device
        inputs = inputs.to(self.device)
        labels = labels.to(self.device)
        
        # Forward pass
        outputs = self.model(inputs)  # Could fail here if shapes don't match
        loss = self.criterion(outputs, labels)
        
        # Backward pass
        self.optimizer.zero_grad()
        loss.backward()  # Automatic differentiation
        self.optimizer.step()
        
        # Calculate accuracy
        _, predicted = torch.max(outputs.data, 1)
        accuracy = (predicted == labels).float().mean().item()
        
        return loss.item(), accuracy
    
    def evaluate(self, 
                 inputs: torch.Tensor, 
                 labels: torch.Tensor) -> Tuple[float, float]:
        """
        Evaluate model on given data
        
        Args:
            inputs: Input tensor [batch_size, 784]
            labels: Label tensor [batch_size]
            
        Returns:
            (loss, accuracy)
        """
        self.model.eval()
        
        with torch.no_grad():
            inputs = inputs.to(self.device)
            labels = labels.to(self.device)
            
            outputs = self.model(inputs)
            loss = self.criterion(outputs, labels)
            
            _, predicted = torch.max(outputs.data, 1)
            accuracy = (predicted == labels).float().mean().item()
            
        return loss.item(), accuracy
    
    def train_epoch(self, 
                   train_batches: List[Tuple[torch.Tensor, torch.Tensor]],
                   test_batches: List[Tuple[torch.Tensor, torch.Tensor]]) -> Dict[str, float]:
        """
        Train for one epoch
        
        Args:
            train_batches: List of (inputs, labels) tuples for training
            test_batches: List of (inputs, labels) tuples for testing
            
        Returns:
            Dictionary with epoch statistics
        """
        start_time = time.time()
        
        # Training phase
        total_train_loss = 0.0
        total_train_acc = 0.0
        num_train_batches = len(train_batches)
        
        for inputs, labels in train_batches:
            batch_loss, batch_acc = self.train_batch(inputs, labels)
            total_train_loss += batch_loss
            total_train_acc += batch_acc
        
        avg_train_loss = total_train_loss / num_train_batches
        avg_train_acc = total_train_acc / num_train_batches
        
        # Evaluation phase
        total_test_loss = 0.0
        total_test_acc = 0.0
        num_test_batches = len(test_batches)
        
        for inputs, labels in test_batches:
            batch_loss, batch_acc = self.evaluate(inputs, labels)
            total_test_loss += batch_loss
            total_test_acc += batch_acc
        
        avg_test_loss = total_test_loss / num_test_batches
        avg_test_acc = total_test_acc / num_test_batches
        
        epoch_time = time.time() - start_time
        
        return {
            'train_loss': avg_train_loss,
            'train_accuracy': avg_train_acc,
            'test_loss': avg_test_loss,
            'test_accuracy': avg_test_acc,
            'time_seconds': epoch_time
        }
    
    def train_epochs(self, 
                    num_epochs: int,
                    train_batches: List[Tuple[torch.Tensor, torch.Tensor]],
                    test_batches: List[Tuple[torch.Tensor, torch.Tensor]],
                    verbose: bool = True) -> List[Dict[str, Any]]:
        """
        Train for multiple epochs
        
        Args:
            num_epochs: Number of epochs to train
            train_batches: Training data batches
            test_batches: Test data batches
            verbose: Whether to print progress
            
        Returns:
            List of epoch statistics
        """
        epoch_stats = []
        
        for epoch in range(num_epochs):
            stats = self.train_epoch(train_batches, test_batches)
            stats['epoch'] = epoch + 1
            epoch_stats.append(stats)
            
            if verbose:
                print(f"Epoch {epoch + 1}/{num_epochs}: "
                      f"Train Loss: {stats['train_loss']:.4f}, "
                      f"Train Acc: {stats['train_accuracy']:.4f}, "
                      f"Test Loss: {stats['test_loss']:.4f}, "
                      f"Test Acc: {stats['test_accuracy']:.4f}, "
                      f"Time: {stats['time_seconds']:.2f}s")
        
        self.epoch_stats = epoch_stats
        return epoch_stats


def demonstrate_runtime_errors() -> None:
    """
    Demonstrate how Python only catches shape errors at runtime
    """
    print("\n=== Runtime Error Demonstration ===")
    print("The following demonstrates Python's runtime error detection:")
    
    model = MNISTNetwork()
    
    # This works fine
    try:
        valid_input = torch.randn(32, 784)  # Correct shape
        output = model(valid_input)
        print(f"✓ Valid input shape {tuple(valid_input.shape)} -> output shape {tuple(output.shape)}")
    except Exception as e:
        print(f"✗ Unexpected error with valid input: {e}")
    
    # This causes a RUNTIME ERROR
    try:
        invalid_input = torch.randn(32, 800)  # Wrong input size!
        output = model(invalid_input)
        print(f"✗ This should have failed but didn't: {tuple(output.shape)}")
    except Exception as e:
        print(f"✓ Runtime error caught with invalid input shape {tuple(invalid_input.shape)}: {e}")
    
    # Demonstrate layer mismatch error
    try:
        print("\n--- Layer Mismatch Demo ---")
        broken_model = nn.Sequential(
            nn.Linear(784, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(), 
            nn.Linear(100, 10)  # Wrong input size! Should be 128
        )
        test_input = torch.randn(1, 784)
        broken_output = broken_model(test_input)
    except Exception as e:
        print(f"✓ Layer mismatch error caught: {e}")
    
    print("Note: Idris would catch ALL these errors at compile-time!")


def create_broken_network_examples() -> None:
    """Examples of networks that would fail in different ways"""
    
    print("\n=== Potential Error Sources in Python ===")
    
    # Example 1: Wrong architecture specification
    print("1. Architecture inconsistencies (caught at runtime):")
    try:
        # This looks wrong but won't fail until you run data through it
        model = MNISTNetwork(
            input_dim=784,
            hidden1_dim=256,
            hidden2_dim=128,
            output_dim=10
        )
        # Manually corrupt a layer (simulating a copy-paste error)
        model.layer2 = nn.Linear(256, 64)  # Should be 128, but Python allows this
        model.layer3 = nn.Linear(128, 10)  # This expects 128 input, but layer2 outputs 64
        
        test_input = torch.randn(1, 784)
        output = model(test_input)  # This is where it fails
        
    except Exception as e:
        print(f"   Runtime error: {e}")
    
    # Example 2: Dynamic shape errors
    print("\n2. Dynamic input shape errors:")
    model = MNISTNetwork()
    dynamic_shapes = [780, 784, 800, 1000]  # Different input sizes
    
    for shape in dynamic_shapes:
        try:
            test_input = torch.randn(1, shape)
            output = model(test_input)
            print(f"   Shape {shape}: ✓ Success")
        except Exception as e:
            print(f"   Shape {shape}: ✗ {e}")


if __name__ == "__main__":
    # Test the network
    print("=== PyTorch Neural Network Test ===")
    
    # Create model
    model = MNISTNetwork()
    print(f"Model created with architecture: {model.input_dim} -> {model.hidden1_dim} -> {model.hidden2_dim} -> {model.output_dim}")
    
    # Print layer shapes
    shapes = model.get_layer_shapes()
    print("\nLayer shapes:")
    for layer_name, (weight_shape, bias_shape) in shapes.items():
        print(f"  {layer_name}: weights {weight_shape}, biases {bias_shape}")
    
    # Test forward pass
    test_input = torch.randn(32, 784)
    output = model(test_input)
    print(f"\nTest forward pass: input {tuple(test_input.shape)} -> output {tuple(output.shape)}")
    
    # Test prediction
    prediction = model.predict(test_input[0])
    print(f"Sample prediction: {prediction.item()}")
    
    # Demonstrate error cases
    demonstrate_runtime_errors()
    create_broken_network_examples()
    
    print("\n=== Comparison with Idris ===")
    print("PyTorch Characteristics:")
    print("• Shape errors detected at runtime")
    print("• Flexible but error-prone")
    print("• Requires extensive testing")
    print("• Fast development but debugging overhead")
    
    print("\nIdris Characteristics:")  
    print("• Shape errors detected at compile-time")
    print("• Rigid but guaranteed correct")
    print("• Self-documenting types")
    print("• Slower development but no runtime shape errors")