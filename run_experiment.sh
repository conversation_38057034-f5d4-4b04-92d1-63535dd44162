#!/bin/bash

# Neural Network Layer Compatibility Verification Experiment
# Comprehensive comparison: Idris dependent types vs Python runtime checking

set -e  # Exit on any error

echo "=== Neural Network Layer Compatibility Verification Experiment ==="
echo "=== Comprehensive Comparison: Idris Dependent Types vs Python Runtime Checking ==="
echo ""

# Configuration
EXPERIMENT_NAME="layer_compatibility_verification"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_DIR="logs"
RESULTS_DIR="results"
PLOTS_DIR="plots"
PYTHON_DIR="python"

# Create directories
mkdir -p "$LOG_DIR" "$RESULTS_DIR" "$PLOTS_DIR"

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_DIR/experiment_run.log"
}

log "Starting experiment: $EXPERIMENT_NAME"
log "Timestamp: $TIMESTAMP"

# Step 1: CRITICAL - Verify Idris2 compilation
log "Step 1: CRITICAL - Verifying Idris2 compilation..."
export PATH="$HOME/.pack/bin:$PATH"

if ! command -v pack &> /dev/null; then
    log "ERROR: pack not found in PATH"
    exit 1
fi

log "Building Idris neural network..."
if pack build nn-mnist.ipkg 2>&1 | tee "$LOG_DIR/idris_build.log"; then
    log "✓ Idris compilation successful - type safety verified"
else
    log "✗ Idris compilation failed"
    exit 1
fi

# Step 2: Test Idris runtime (expected PJRT failure)
log "Step 2: Testing Idris runtime (expected PJRT failure)..."
if timeout 30s ./build/exec/nn-mnist 2>&1 | tee "$LOG_DIR/idris_runtime.log"; then
    log "✓ Idris runtime completed"
else
    log "✓ Idris runtime failed as expected (PJRT incompatibility)"
fi

# Step 3: Python environment setup
log "Step 3: Setting up Python environment..."
source venv/bin/activate

if pip install -r python/requirements.txt 2>&1 | tee "$LOG_DIR/python_install.log"; then
    log "✓ Python dependencies installed"
else
    log "✗ Python dependency installation failed"
    exit 1
fi

# Step 4: Create datasets
log "Step 4: Creating datasets..."
cd "$PYTHON_DIR"
if python dataset_creation.py 2>&1 | tee "../$LOG_DIR/dataset_creation.log"; then
    log "✓ Datasets created successfully"
else
    log "✗ Dataset creation failed"
    exit 1
fi

# Step 5: Run compatibility tests
log "Step 5: Running layer compatibility tests..."
if python layer_compatibility_testing.py 2>&1 | tee "../$LOG_DIR/compatibility_tests.log"; then
    log "✓ Compatibility tests completed"
else
    log "✗ Compatibility tests failed"
    exit 1
fi

# Step 6: Generate visualizations
log "Step 6: Generating visualizations..."
if python visualization_system.py 2>&1 | tee "../$LOG_DIR/visualization.log"; then
    log "✓ Visualizations generated"
else
    log "✗ Visualization generation failed"
fi

cd ..

log "Experiment completed successfully!"
echo ""
echo "Results available in:"
echo "  - Plots: $PLOTS_DIR/"
echo "  - Results: $RESULTS_DIR/"
echo "  - Logs: $LOG_DIR/"
